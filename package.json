{"name": "interstellar-portfolio", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "predeploy": "npm run build", "deploy": "gh-pages -d dist"}, "dependencies": {"@emailjs/browser": "^4.4.1", "@google-analytics/data": "^5.1.0", "@react-three/drei": "^9.122.0", "@react-three/fiber": "^8.18.0", "@tsparticles/engine": "^3.8.1", "@tsparticles/preset-confetti": "^3.2.0", "@tsparticles/react": "^3.0.0", "@tsparticles/slim": "^3.8.1", "@types/three": "^0.160.0", "framer-motion": "^10.16.16", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-ga4": "^2.1.0", "react-intersection-observer": "^9.5.3", "react-particles": "^2.12.2", "react-scroll-parallax": "^3.4.5", "three": "^0.160.1", "typed.js": "^2.1.0"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "gh-pages": "^6.3.0", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}