import React, { useRef, useMemo } from "react";
import { Canvas, useFrame, extend, useLoader } from "@react-three/fiber";
import { shaderMaterial } from "@react-three/drei";
import * as THREE from "three";

// Create a starfield texture for background
const createStarfieldTexture = () => {
  const canvas = document.createElement("canvas");
  canvas.width = 512;
  canvas.height = 512;
  const ctx = canvas.getContext("2d")!;

  // Black background
  ctx.fillStyle = "#000000";
  ctx.fillRect(0, 0, 512, 512);

  // Add stars
  for (let i = 0; i < 1000; i++) {
    const x = Math.random() * 512;
    const y = Math.random() * 512;
    const brightness = Math.random();
    const size = Math.random() * 2;

    ctx.fillStyle = `rgba(255, 255, 255, ${brightness})`;
    ctx.beginPath();
    ctx.arc(x, y, size, 0, Math.PI * 2);
    ctx.fill();
  }

  const texture = new THREE.CanvasTexture(canvas);
  texture.wrapS = THREE.RepeatWrapping;
  texture.wrapT = THREE.RepeatWrapping;
  return texture;
};

// Enhanced vertex shader with gravitational effects
const vertexShader = `
varying vec2 vUv;
varying vec3 vPosition;
varying vec3 vNormal;
varying vec3 vWorldPosition;

uniform float time;
uniform float blackHoleRadius;
uniform vec3 blackHolePosition;

void main() {
    vUv = uv;
    vNormal = normalize(normalMatrix * normal);
    vPosition = position;

    vec4 worldPosition = modelMatrix * vec4(position, 1.0);
    vWorldPosition = worldPosition.xyz;

    // Frame-dragging effect
    float distanceToBlackHole = length(worldPosition.xyz - blackHolePosition);
    float frameDragging = blackHoleRadius / (distanceToBlackHole * distanceToBlackHole);
    frameDragging = clamp(frameDragging * 0.05, 0.0, 0.02);

    float angle = frameDragging * time * 0.1;
    mat2 rotation = mat2(cos(angle), -sin(angle), sin(angle), cos(angle));
    worldPosition.xz = blackHolePosition.xz + rotation * (worldPosition.xz - blackHolePosition.xz);

    gl_Position = projectionMatrix * viewMatrix * worldPosition;
}
`;

// Enhanced fragment shader with scientific accuracy
const fragmentShader = `
varying vec2 vUv;
varying vec3 vPosition;
varying vec3 vNormal;
varying vec3 vWorldPosition;

uniform float time;
uniform float blackHoleRadius;
uniform vec3 blackHolePosition;
uniform vec3 cameraPosition;
uniform sampler2D starfieldTexture;
uniform float intensity;

// Constants for gravitational lensing
const float Rs = 0.1; // Schwarzschild radius
const float photonSphereRadius = 1.5 * Rs;
const float innerStableOrbit = 3.0 * Rs;
const float PI = 3.14159265359;

// Noise function for turbulence
float noise(vec2 p) {
    return fract(sin(dot(p, vec2(12.9898, 78.233))) * 43758.5453);
}

float fbm(vec2 p) {
    float value = 0.0;
    float amplitude = 0.5;
    float frequency = 1.0;

    for (int i = 0; i < 4; i++) {
        value += amplitude * noise(p * frequency);
        amplitude *= 0.5;
        frequency *= 2.0;
    }

    return value;
}

// Gravitational lensing
vec2 gravitationalLensing(vec2 uv, vec3 blackHolePos) {
    vec2 center = vec2(0.5, 0.5);
    vec2 delta = uv - center;
    float r = length(delta);

    if (r < 0.001) return uv;

    float deflection = Rs / r;

    if (r < photonSphereRadius) {
        float strongDeflection = Rs / (r * r) * 2.0;
        deflection += strongDeflection;

        float ringStrength = smoothstep(photonSphereRadius * 0.8, photonSphereRadius, r);
        deflection *= (1.0 + ringStrength * 3.0);
    }

    vec2 lensedUv = center + delta * (1.0 + deflection * 0.3);
    return fract(lensedUv + 1.0);
}

// Doppler shift calculation
float dopplerShift(vec2 pos, float rotationSpeed) {
    float angle = atan(pos.y, pos.x);
    float velocity = rotationSpeed * length(pos);
    return velocity * cos(angle) * 0.1;
}

void main() {
    vec2 center = vec2(0.5, 0.5);
    vec2 uv = vUv;
    float r = length(uv - center);

    vec2 lensedUv = gravitationalLensing(uv, blackHolePosition);

    // Event horizon - pure black
    if (r < Rs) {
        gl_FragColor = vec4(0.0, 0.0, 0.0, 1.0);
        return;
    }

    // Accretion disk
    if (r > innerStableOrbit && r < 0.4) {
        float angle = atan(uv.y - center.y, uv.x - center.x);

        float temperature = 1.0 - smoothstep(innerStableOrbit, 0.4, r);
        temperature = pow(temperature, 0.7);

        float spiral = sin(angle * 3.0 - time * 2.0 + r * 20.0) * 0.1;
        float turbulence = fbm(uv * 8.0 + time * 0.5) * 0.3;
        temperature *= (0.7 + spiral + turbulence);

        float rotationSpeed = 1.0 / sqrt(r);
        float doppler = dopplerShift(uv - center, rotationSpeed);

        // Double Negative color palette
        vec3 diskColor;
        if (temperature > 0.85) {
            diskColor = vec3(1.0, 1.0, 1.0);
        } else if (temperature > 0.6) {
            diskColor = mix(vec3(1.0, 0.95, 0.8), vec3(1.0, 1.0, 1.0), (temperature - 0.6) * 4.0);
        } else if (temperature > 0.3) {
            diskColor = mix(vec3(1.0, 0.7, 0.2), vec3(1.0, 0.95, 0.8), (temperature - 0.3) * 3.33);
        } else {
            diskColor = mix(vec3(0.6, 0.2, 0.05), vec3(1.0, 0.7, 0.2), temperature * 3.33);
        }

        diskColor.r *= (1.0 - doppler * 0.3);
        diskColor.b *= (1.0 + doppler * 0.3);

        float intensity = temperature * smoothstep(0.4, 0.35, r) * smoothstep(innerStableOrbit, innerStableOrbit + 0.02, r);

        gl_FragColor = vec4(diskColor * intensity, intensity);
        return;
    }

    // Photon sphere effects
    if (r < photonSphereRadius && r > Rs) {
        float ringIntensity = smoothstep(photonSphereRadius * 0.9, photonSphereRadius, r);
        vec3 ringColor = vec3(1.0, 0.8, 0.4) * ringIntensity * 0.8;

        vec4 background = texture2D(starfieldTexture, lensedUv);
        float redshift = sqrt(1.0 - Rs / r);
        background.rgb *= redshift;

        gl_FragColor = vec4(background.rgb + ringColor, 1.0);
        return;
    }

    // Weak lensing region
    if (r < 0.8) {
        vec4 background = texture2D(starfieldTexture, lensedUv);
        float glowStrength = smoothstep(0.8, photonSphereRadius, r);
        vec3 glow = vec3(1.0, 0.7, 0.3) * glowStrength * 0.2;

        gl_FragColor = vec4(background.rgb + glow, 1.0);
        return;
    }

    // Far field
    vec4 starfield = texture2D(starfieldTexture, lensedUv);
    gl_FragColor = starfield;
}
`;

const GargantuaMaterial = shaderMaterial(
  {
    time: 0,
    blackHoleRadius: 0.1,
    blackHolePosition: new THREE.Vector3(0, 0, 0),
    cameraPosition: new THREE.Vector3(0, 0, 1.5),
    starfieldTexture: null,
    intensity: 1.0,
  },
  vertexShader,
  fragmentShader
);

// Register material for JSX
// @ts-ignore
extend({ GargantuaMaterial });

function Gargantua() {
  const mesh = useRef<THREE.Mesh>(null!);
  const materialRef = useRef<any>(null!);

  // Create starfield texture
  const starfieldTexture = useMemo(() => createStarfieldTexture(), []);

  useFrame((state) => {
    if (mesh.current && materialRef.current) {
      // Slow accretion disk rotation
      mesh.current.rotation.z += 0.0008;

      // Update shader uniforms
      materialRef.current.time = state.clock.elapsedTime;
      materialRef.current.cameraPosition = state.camera.position;
      materialRef.current.starfieldTexture = starfieldTexture;
    }
  });

  return (
    <mesh ref={mesh} position={[0, 0, 0]}>
      <planeGeometry args={[4, 4, 256, 256]} />
      {/* @ts-ignore */}
      <gargantuaMaterial
        ref={materialRef}
        time={0}
        blackHoleRadius={0.1}
        blackHolePosition={[0, 0, 0]}
        cameraPosition={[0, 0, 1.5]}
        starfieldTexture={starfieldTexture}
        intensity={1.0}
      />
    </mesh>
  );
}

interface GargantuaBlackHoleProps {
  className?: string;
}

const GargantuaBlackHole: React.FC<GargantuaBlackHoleProps> = ({
  className = "w-full h-full min-h-screen absolute inset-0 z-10 bg-transparent",
}) => (
  <div className={className}>
    <Canvas
      camera={{ position: [0, 0, 1.5], fov: 50 }}
      gl={{
        antialias: true,
        alpha: true,
        powerPreference: "high-performance",
      }}
    >
      <Gargantua />
    </Canvas>
  </div>
);

export default GargantuaBlackHole;
