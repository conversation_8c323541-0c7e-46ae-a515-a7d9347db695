import React, { useRef } from 'react';
import { Canvas, useFrame, extend } from '@react-three/fiber';
import { shaderMaterial } from '@react-three/drei';
import * as THREE from 'three';

// GLSL shader for Gargantua (simplified, based on <PERSON><PERSON>'s model)
const vertexShader = `
varying vec2 vUv;
void main() {
  vUv = uv;
  gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
}
`;

const fragmentShader = `
// Based on Double Negative's Interstellar Gargantua
varying vec2 vUv;

// Parameters for accretion disk
const float diskRadius = 0.45;
const float diskThickness = 0.08;
const float eventHorizon = 0.28;

void main() {
  vec2 uv = vUv * 2.0 - 1.0;
  float r = length(uv);
  float theta = atan(uv.y, uv.x);

  // Black hole event horizon
  if (r < eventHorizon) {
    gl_FragColor = vec4(0.0, 0.0, 0.0, 1.0);
    return;
  }

  // Accretion disk (deep orange/gold/white)
  float disk = smoothstep(diskRadius, diskRadius - diskThickness, abs(uv.y));
  float colorMix = smoothstep(diskRadius, diskRadius - diskThickness * 0.5, abs(uv.y));
  vec3 diskColor = mix(vec3(1.0, 0.7, 0.2), vec3(1.0, 0.95, 0.7), colorMix); // gold to white
  diskColor = mix(diskColor, vec3(0.8, 0.3, 0.05), 1.0 - colorMix); // deep orange

  // Gravitational lensing (halo)
  float lens = smoothstep(eventHorizon + 0.01, eventHorizon + 0.08, r);
  vec3 lensColor = mix(vec3(0.0), vec3(1.0, 0.8, 0.4), lens);

  // Starfield background
  vec3 bg = vec3(0.01, 0.01, 0.02);

  vec3 color = bg;
  color = mix(color, diskColor, disk);
  color = mix(color, lensColor, lens);

  gl_FragColor = vec4(color, 1.0);
}
`;

const GargantuaMaterial = shaderMaterial(
  {},
  vertexShader,
  fragmentShader
);

// Register material for JSX
// @ts-ignore
extend({ GargantuaMaterial });

function Gargantua() {
  const mesh = useRef<THREE.Mesh>(null!);
  useFrame(() => {
    if (mesh.current) {
      mesh.current.rotation.z += 0.0005; // slow disk rotation
    }
  });
  return (
    <mesh ref={mesh} position={[0, 0, 0]}>
      <planeGeometry args={[2, 2, 128, 128]} />
      {/* @ts-ignore */}
      <gargantuaMaterial />
    </mesh>
  );
}

const GargantuaBlackHole: React.FC = () => (
  <div className="w-full h-full min-h-screen absolute inset-0 z-10 bg-transparent">
    <Canvas camera={{ position: [0, 0, 1.5], fov: 50 }}>
      <Gargantua />
    </Canvas>
  </div>
);

export default GargantuaBlackHole; 