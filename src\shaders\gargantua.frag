// Gargantua Black Hole Fragment Shader
// Scientifically accurate gravitational lensing based on <PERSON><PERSON>'s models
// Colors based on Double Negative's rendering: black, deep orange, gold, white

varying vec2 vUv;
varying vec3 vPosition;
varying vec3 vNormal;
varying vec3 vWorldPosition;

uniform float time;
uniform float blackHoleRadius;
uniform vec3 blackHolePosition;
uniform vec3 cameraPosition;
uniform sampler2D starfieldTexture;
uniform float accretionDiskRadius;
uniform float eventHorizonRadius;

// Constants for gravitational lensing
const float G = 6.67430e-11;
const float c = 299792458.0;
const float M = 4.154e6; // Gargantua mass in solar masses
const float Rs = 2.0 * G * M / (c * c); // Schwarzschild radius

// Noise function for accretion disk turbulence
float noise(vec2 p) {
    return fract(sin(dot(p, vec2(12.9898, 78.233))) * 43758.5453);
}

float fbm(vec2 p) {
    float value = 0.0;
    float amplitude = 0.5;
    float frequency = 1.0;
    
    for (int i = 0; i < 4; i++) {
        value += amplitude * noise(p * frequency);
        amplitude *= 0.5;
        frequency *= 2.0;
    }
    
    return value;
}

// Gravitational lensing calculation
vec2 gravitationalLensing(vec3 rayDirection, vec3 blackHolePos, float mass) {
    float distance = length(vWorldPosition - blackHolePos);
    float impactParameter = distance * sin(acos(dot(normalize(vWorldPosition - blackHolePos), rayDirection)));
    
    // Einstein ring radius
    float einsteinRadius = sqrt(4.0 * G * mass / (c * c) * distance);
    
    // Deflection angle (simplified)
    float deflectionAngle = 4.0 * G * mass / (c * c * impactParameter);
    
    // Apply lensing distortion
    vec2 lensedUv = vUv;
    if (impactParameter < einsteinRadius * 2.0) {
        float lensStrength = einsteinRadius / impactParameter;
        lensedUv += (vUv - 0.5) * lensStrength * 0.1;
    }
    
    return lensedUv;
}

void main() {
    vec3 rayDirection = normalize(vWorldPosition - cameraPosition);
    float distanceToBlackHole = length(vWorldPosition - blackHolePosition);
    
    // Apply gravitational lensing
    vec2 lensedUv = gravitationalLensing(rayDirection, blackHolePosition, M);
    
    // Event horizon check
    if (distanceToBlackHole < eventHorizonRadius) {
        gl_FragColor = vec4(0.0, 0.0, 0.0, 1.0); // Pure black
        return;
    }
    
    // Accretion disk
    float diskDistance = abs(vWorldPosition.y - blackHolePosition.y);
    float radialDistance = length(vWorldPosition.xz - blackHolePosition.xz);
    
    if (diskDistance < 0.5 && radialDistance > eventHorizonRadius && radialDistance < accretionDiskRadius) {
        // Temperature-based coloring (hotter = whiter, cooler = orange/red)
        float temperature = 1.0 - (radialDistance - eventHorizonRadius) / (accretionDiskRadius - eventHorizonRadius);
        temperature = pow(temperature, 0.5); // More realistic temperature falloff
        
        // Add turbulence
        float turbulence = fbm(vWorldPosition.xz * 0.1 + time * 0.1);
        temperature *= (0.8 + 0.4 * turbulence);
        
        // Doppler shift effect
        float rotationSpeed = sqrt(G * M / radialDistance);
        float dopplerShift = rotationSpeed / c;
        
        // Color based on temperature and Doppler shift
        vec3 diskColor;
        if (temperature > 0.8) {
            // Hot regions: white to pale blue
            diskColor = mix(vec3(1.0, 0.9, 0.8), vec3(1.0, 1.0, 1.0), (temperature - 0.8) * 5.0);
        } else if (temperature > 0.5) {
            // Medium regions: gold to white
            diskColor = mix(vec3(1.0, 0.8, 0.2), vec3(1.0, 0.9, 0.8), (temperature - 0.5) * 3.33);
        } else {
            // Cool regions: deep orange to gold
            diskColor = mix(vec3(0.8, 0.3, 0.1), vec3(1.0, 0.8, 0.2), temperature * 2.0);
        }
        
        // Apply Doppler shift
        if (dot(rayDirection, normalize(vWorldPosition - blackHolePosition)) > 0.0) {
            diskColor *= (1.0 + dopplerShift); // Blue shift
        } else {
            diskColor *= (1.0 - dopplerShift * 0.5); // Red shift
        }
        
        // Intensity based on viewing angle and distance
        float intensity = 1.0 / (1.0 + distanceToBlackHole * 0.1);
        intensity *= (1.0 - diskDistance * 2.0); // Fade at disk edges
        
        gl_FragColor = vec4(diskColor * intensity, intensity);
        return;
    }
    
    // Photon sphere and gravitational lensing effects
    float photonSphereRadius = 1.5 * eventHorizonRadius;
    if (distanceToBlackHole < photonSphereRadius * 2.0) {
        // Multiple image effect from gravitational lensing
        vec2 distortedUv = lensedUv;
        
        // Create ring-like distortions
        float ringEffect = sin(distanceToBlackHole * 10.0 - time * 2.0) * 0.1;
        distortedUv += ringEffect;
        
        // Sample background with distortion
        vec4 background = texture2D(starfieldTexture, distortedUv);
        
        // Add gravitational redshift
        float redshift = sqrt(1.0 - eventHorizonRadius / distanceToBlackHole);
        background.rgb *= redshift;
        
        // Add subtle orange glow from gravitational effects
        float glowStrength = 1.0 - (distanceToBlackHole / (photonSphereRadius * 2.0));
        vec3 glow = vec3(1.0, 0.6, 0.2) * glowStrength * 0.3;
        
        gl_FragColor = vec4(background.rgb + glow, 1.0);
        return;
    }
    
    // Far field: normal starfield with minimal lensing
    vec4 starfield = texture2D(starfieldTexture, lensedUv);
    gl_FragColor = starfield;
}
