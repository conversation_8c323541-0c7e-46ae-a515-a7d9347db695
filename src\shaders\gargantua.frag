// Gargantua Black Hole Fragment Shader
// Scientifically accurate gravitational lensing based on <PERSON><PERSON>'s models
// Colors based on Double Negative's rendering: black, deep orange, gold, white

varying vec2 vUv;
varying vec3 vPosition;
varying vec3 vNormal;
varying vec3 vWorldPosition;

uniform float time;
uniform float blackHoleRadius;
uniform vec3 blackHolePosition;
uniform vec3 cameraPosition;
uniform sampler2D starfieldTexture;
uniform float accretionDiskRadius;
uniform float eventHorizonRadius;
uniform float intensity;
uniform vec2 resolution;

// Constants for gravitational lensing (normalized for shader)
const float Rs = 0.1; // Schwarzschild radius (normalized)
const float photonSphereRadius = 1.5 * Rs; // Photon sphere at 1.5 Rs
const float innerStableOrbit = 3.0 * Rs; // Innermost stable circular orbit
const float PI = 3.14159265359;
const float TWO_PI = 6.28318530718;

// Noise function for accretion disk turbulence
float noise(vec2 p) {
    return fract(sin(dot(p, vec2(12.9898, 78.233))) * 43758.5453);
}

float fbm(vec2 p) {
    float value = 0.0;
    float amplitude = 0.5;
    float frequency = 1.0;
    
    for (int i = 0; i < 4; i++) {
        value += amplitude * noise(p * frequency);
        amplitude *= 0.5;
        frequency *= 2.0;
    }
    
    return value;
}

// Advanced gravitational lensing based on Kip Thorne's models
vec2 gravitationalLensing(vec2 uv, vec3 blackHolePos) {
    vec2 center = vec2(0.5, 0.5);
    vec2 delta = uv - center;
    float r = length(delta);

    // Avoid division by zero
    if (r < 0.001) return uv;

    // Schwarzschild metric deflection
    float deflection = Rs / r;

    // Strong field regime (near photon sphere)
    if (r < photonSphereRadius) {
        // Multiple image formation
        float strongDeflection = Rs / (r * r) * 2.0;
        deflection += strongDeflection;

        // Einstein ring effect
        float ringStrength = smoothstep(photonSphereRadius * 0.8, photonSphereRadius, r);
        deflection *= (1.0 + ringStrength * 3.0);
    }

    // Apply lensing distortion
    vec2 lensedUv = center + delta * (1.0 + deflection * 0.3);

    // Wrap coordinates for multiple images
    lensedUv = fract(lensedUv + 1.0);

    return lensedUv;
}

// Doppler shift calculation for accretion disk
float dopplerShift(vec2 pos, float rotationSpeed) {
    float angle = atan(pos.y, pos.x);
    float velocity = rotationSpeed * length(pos);
    return velocity * cos(angle) * 0.1; // Simplified relativistic Doppler
}

void main() {
    vec2 center = vec2(0.5, 0.5);
    vec2 uv = vUv;
    float r = length(uv - center);

    // Apply gravitational lensing
    vec2 lensedUv = gravitationalLensing(uv, blackHolePosition);

    // Event horizon - pure black
    if (r < Rs) {
        gl_FragColor = vec4(0.0, 0.0, 0.0, 1.0);
        return;
    }

    // Accretion disk - between innermost stable orbit and outer edge
    if (r > innerStableOrbit && r < 0.4) {
        float angle = atan(uv.y - center.y, uv.x - center.x);

        // Temperature profile - hotter closer to black hole
        float temperature = 1.0 - smoothstep(innerStableOrbit, 0.4, r);
        temperature = pow(temperature, 0.7); // More realistic falloff

        // Add turbulence and spiral structure
        float spiral = sin(angle * 3.0 - time * 2.0 + r * 20.0) * 0.1;
        float turbulence = fbm(uv * 8.0 + time * 0.5) * 0.3;
        temperature *= (0.7 + spiral + turbulence);

        // Doppler shift based on rotation
        float rotationSpeed = 1.0 / sqrt(r); // Keplerian velocity profile
        float doppler = dopplerShift(uv - center, rotationSpeed);

        // Double Negative color palette
        vec3 diskColor;
        if (temperature > 0.85) {
            // Hottest regions: brilliant white
            diskColor = vec3(1.0, 1.0, 1.0);
        } else if (temperature > 0.6) {
            // Hot regions: white to pale gold
            diskColor = mix(vec3(1.0, 0.95, 0.8), vec3(1.0, 1.0, 1.0), (temperature - 0.6) * 4.0);
        } else if (temperature > 0.3) {
            // Medium regions: gold to deep orange
            diskColor = mix(vec3(1.0, 0.7, 0.2), vec3(1.0, 0.95, 0.8), (temperature - 0.3) * 3.33);
        } else {
            // Cool regions: deep orange to dark red
            diskColor = mix(vec3(0.6, 0.2, 0.05), vec3(1.0, 0.7, 0.2), temperature * 3.33);
        }

        // Apply Doppler shift coloring
        diskColor.r *= (1.0 - doppler * 0.3); // Red shift on receding side
        diskColor.b *= (1.0 + doppler * 0.3); // Blue shift on approaching side

        // Intensity falloff
        float intensity = temperature * smoothstep(0.4, 0.35, r) * smoothstep(innerStableOrbit, innerStableOrbit + 0.02, r);

        gl_FragColor = vec4(diskColor * intensity, intensity);
        return;
    }

    // Photon sphere effects - critical radius for light orbits
    if (r < photonSphereRadius && r > Rs) {
        // Einstein ring formation
        float ringIntensity = smoothstep(photonSphereRadius * 0.9, photonSphereRadius, r);
        vec3 ringColor = vec3(1.0, 0.8, 0.4) * ringIntensity * 0.8;

        // Multiple image distortion
        vec2 distortedUv = lensedUv;
        float distortionStrength = (photonSphereRadius - r) / photonSphereRadius;

        // Create secondary and tertiary images
        vec2 secondaryUv = center + (uv - center) * (1.0 + distortionStrength * 2.0);
        vec2 tertiaryUv = center + (uv - center) * (1.0 - distortionStrength * 0.5);

        // Sample background with multiple images
        vec4 primary = texture2D(starfieldTexture, distortedUv);
        vec4 secondary = texture2D(starfieldTexture, fract(secondaryUv)) * 0.3;
        vec4 tertiary = texture2D(starfieldTexture, fract(tertiaryUv)) * 0.1;

        vec4 background = primary + secondary + tertiary;

        // Gravitational redshift
        float redshift = sqrt(1.0 - Rs / r);
        background.rgb *= redshift;

        gl_FragColor = vec4(background.rgb + ringColor, 1.0);
        return;
    }

    // Weak lensing region
    if (r < 0.8) {
        vec4 background = texture2D(starfieldTexture, lensedUv);

        // Subtle gravitational glow
        float glowStrength = smoothstep(0.8, photonSphereRadius, r);
        vec3 glow = vec3(1.0, 0.7, 0.3) * glowStrength * 0.2;

        gl_FragColor = vec4(background.rgb + glow, 1.0);
        return;
    }

    // Far field: minimal lensing
    vec4 starfield = texture2D(starfieldTexture, lensedUv);
    gl_FragColor = starfield;
}
