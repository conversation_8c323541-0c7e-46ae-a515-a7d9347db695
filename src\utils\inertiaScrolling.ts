// Enhanced inertia-based scrolling with gravity-style physics

export interface InertiaConfig {
  friction: number;
  tension: number;
  mass: number;
  velocity: number;
  restThreshold: number;
  maxVelocity: number;
}

export interface ScrollState {
  position: number;
  velocity: number;
  isScrolling: boolean;
  target: number;
}

export class InertiaScrollController {
  private config: InertiaConfig;
  private state: ScrollState;
  private animationId: number | null = null;
  private lastTime: number = 0;
  private callbacks: Array<(state: ScrollState) => void> = [];
  private element: HTMLElement | null = null;

  constructor(element: HTMLElement, config: Partial<InertiaConfig> = {}) {
    this.element = element;
    this.config = {
      friction: 0.92,
      tension: 0.1,
      mass: 1,
      velocity: 0,
      restThreshold: 0.5,
      maxVelocity: 50,
      ...config,
    };

    this.state = {
      position: 0,
      velocity: 0,
      isScrolling: false,
      target: 0,
    };

    this.init();
  }

  private init() {
    if (!this.element) return;

    // Prevent default scroll behavior
    this.element.style.overflow = 'hidden';
    
    // Add event listeners
    this.element.addEventListener('wheel', this.handleWheel.bind(this), { passive: false });
    this.element.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: false });
    this.element.addEventListener('touchmove', this.handleTouchMove.bind(this), { passive: false });
    this.element.addEventListener('touchend', this.handleTouchEnd.bind(this));
  }

  private handleWheel(e: WheelEvent) {
    e.preventDefault();
    
    const delta = e.deltaY * 0.5; // Reduce sensitivity
    this.addVelocity(delta);
  }

  private touchStartY: number = 0;
  private lastTouchY: number = 0;
  private touchVelocity: number = 0;
  private touchTime: number = 0;

  private handleTouchStart(e: TouchEvent) {
    e.preventDefault();
    this.touchStartY = e.touches[0].clientY;
    this.lastTouchY = this.touchStartY;
    this.touchVelocity = 0;
    this.touchTime = performance.now();
    this.stopAnimation();
  }

  private handleTouchMove(e: TouchEvent) {
    e.preventDefault();
    const currentY = e.touches[0].clientY;
    const deltaY = this.lastTouchY - currentY;
    const currentTime = performance.now();
    const deltaTime = currentTime - this.touchTime;
    
    if (deltaTime > 0) {
      this.touchVelocity = deltaY / deltaTime * 16; // Convert to 60fps equivalent
    }
    
    this.addVelocity(deltaY);
    this.lastTouchY = currentY;
    this.touchTime = currentTime;
  }

  private handleTouchEnd(e: TouchEvent) {
    // Apply momentum based on touch velocity
    this.addVelocity(this.touchVelocity * 10);
  }

  private addVelocity(delta: number) {
    this.state.velocity += delta * this.config.tension;
    this.state.velocity = Math.max(-this.config.maxVelocity, 
                                  Math.min(this.config.maxVelocity, this.state.velocity));
    
    if (!this.state.isScrolling) {
      this.startAnimation();
    }
  }

  private startAnimation() {
    if (this.animationId) return;
    
    this.state.isScrolling = true;
    this.lastTime = performance.now();
    this.animationId = requestAnimationFrame(this.animate.bind(this));
  }

  private stopAnimation() {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }
    this.state.isScrolling = false;
  }

  private animate(currentTime: number) {
    const deltaTime = currentTime - this.lastTime;
    this.lastTime = currentTime;

    // Apply physics
    this.updatePhysics(deltaTime);

    // Update position
    this.state.position += this.state.velocity;

    // Apply constraints
    this.applyConstraints();

    // Notify callbacks
    this.notifyCallbacks();

    // Check if we should continue animating
    if (Math.abs(this.state.velocity) > this.config.restThreshold) {
      this.animationId = requestAnimationFrame(this.animate.bind(this));
    } else {
      this.stopAnimation();
      this.state.velocity = 0;
    }
  }

  private updatePhysics(deltaTime: number) {
    // Apply friction
    this.state.velocity *= Math.pow(this.config.friction, deltaTime / 16);

    // Apply gravity-like attraction to target if set
    if (this.state.target !== this.state.position) {
      const distance = this.state.target - this.state.position;
      const force = distance * this.config.tension * 0.1;
      this.state.velocity += force / this.config.mass;
    }
  }

  private applyConstraints() {
    if (!this.element) return;

    const maxScroll = this.element.scrollHeight - this.element.clientHeight;
    
    if (this.state.position < 0) {
      this.state.position = 0;
      this.state.velocity *= -0.3; // Bounce effect
    } else if (this.state.position > maxScroll) {
      this.state.position = maxScroll;
      this.state.velocity *= -0.3; // Bounce effect
    }
  }

  private notifyCallbacks() {
    this.callbacks.forEach(callback => callback({ ...this.state }));
  }

  public onUpdate(callback: (state: ScrollState) => void) {
    this.callbacks.push(callback);
    return () => {
      const index = this.callbacks.indexOf(callback);
      if (index > -1) {
        this.callbacks.splice(index, 1);
      }
    };
  }

  public scrollTo(target: number, smooth: boolean = true) {
    this.state.target = target;
    
    if (smooth) {
      const distance = target - this.state.position;
      this.state.velocity = distance * 0.1;
      this.startAnimation();
    } else {
      this.state.position = target;
      this.state.velocity = 0;
      this.notifyCallbacks();
    }
  }

  public getState(): ScrollState {
    return { ...this.state };
  }

  public updateConfig(newConfig: Partial<InertiaConfig>) {
    this.config = { ...this.config, ...newConfig };
  }

  public destroy() {
    if (!this.element) return;

    this.stopAnimation();
    
    this.element.removeEventListener('wheel', this.handleWheel.bind(this));
    this.element.removeEventListener('touchstart', this.handleTouchStart.bind(this));
    this.element.removeEventListener('touchmove', this.handleTouchMove.bind(this));
    this.element.removeEventListener('touchend', this.handleTouchEnd.bind(this));
    
    this.callbacks = [];
    this.element = null;
  }
}

// React hook for inertia scrolling
export const useInertiaScroll = (
  elementRef: React.RefObject<HTMLElement>,
  config: Partial<InertiaConfig> = {}
) => {
  const [controller, setController] = React.useState<InertiaScrollController | null>(null);
  const [scrollState, setScrollState] = React.useState<ScrollState>({
    position: 0,
    velocity: 0,
    isScrolling: false,
    target: 0,
  });

  React.useEffect(() => {
    if (!elementRef.current) return;

    const newController = new InertiaScrollController(elementRef.current, config);
    setController(newController);

    const unsubscribe = newController.onUpdate(setScrollState);

    return () => {
      unsubscribe();
      newController.destroy();
    };
  }, [elementRef.current]);

  const scrollTo = React.useCallback((target: number, smooth: boolean = true) => {
    controller?.scrollTo(target, smooth);
  }, [controller]);

  const updateConfig = React.useCallback((newConfig: Partial<InertiaConfig>) => {
    controller?.updateConfig(newConfig);
  }, [controller]);

  return {
    scrollState,
    scrollTo,
    updateConfig,
    controller,
  };
};

export default InertiaScrollController;
