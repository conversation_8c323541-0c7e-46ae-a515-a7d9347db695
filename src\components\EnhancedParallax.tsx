import React, { useRef, useEffect, useState } from "react";
import { motion, useScroll, useTransform, useSpring } from "framer-motion";
import { useDeviceDetection } from "../hooks/useDeviceDetection";
import ParticleBackground from "./ParticleBackground";
import GargantuaBlackHole from "./GargantuaBlackHole";
import {
  getGlobalPerformanceManager,
  PerformanceConfig,
} from "../utils/performanceOptimization";
import { zIndex } from "../utils/layoutSystem";

interface ParallaxLayerProps {
  children: React.ReactNode;
  speed: number;
  className?: string;
  zIndex?: number;
  opacity?: number;
  scale?: number;
}

const ParallaxLayer: React.FC<ParallaxLayerProps> = ({
  children,
  speed,
  className = "",
  zIndex = 0,
  opacity = 1,
  scale = 1,
}) => {
  const ref = useRef<HTMLDivElement>(null);
  const { scrollY } = useScroll();
  const deviceInfo = useDeviceDetection();
  const [performanceConfig, setPerformanceConfig] =
    useState<PerformanceConfig | null>(null);

  // Get performance configuration
  useEffect(() => {
    const manager = getGlobalPerformanceManager();
    setPerformanceConfig(manager.getConfig());

    const unsubscribe = manager.onQualityAdjustment((config) => {
      setPerformanceConfig(config);
    });

    return () => {
      // Note: We don't destroy the manager here as it's global
    };
  }, []);

  // Respect accessibility and performance preferences
  const shouldAnimate =
    performanceConfig?.enableParallax &&
    performanceConfig?.enableAnimations &&
    !deviceInfo.prefersReducedMotion;

  // Adaptive spring configuration based on performance
  const springConfig = {
    stiffness: performanceConfig?.shaderQuality === "high" ? 100 : 60,
    damping: performanceConfig?.shaderQuality === "high" ? 30 : 40,
    restDelta: 0.001,
    mass: 1 + Math.abs(speed) * 0.1, // Heavier layers move more slowly
  };

  // Transform scroll position with physics-based easing
  const y = useTransform(scrollY, [0, 1000], [0, speed * 100]);
  const smoothY = useSpring(y, springConfig);

  // Depth-based effects (reduced for lower performance)
  const parallaxOpacity = useTransform(
    scrollY,
    [0, 500, 1000],
    [
      opacity,
      opacity * (performanceConfig?.shaderQuality === "high" ? 0.8 : 0.9),
      opacity * 0.6,
    ]
  );
  const parallaxScale = useTransform(
    scrollY,
    [0, 1000],
    [
      scale,
      scale *
        (1 +
          Math.abs(speed) *
            (performanceConfig?.shaderQuality === "high" ? 0.05 : 0.02)),
    ]
  );

  return (
    <motion.div
      ref={ref}
      className={`absolute inset-0 ${className}`}
      style={{
        zIndex,
        y: shouldAnimate ? smoothY : 0,
        opacity: shouldAnimate ? parallaxOpacity : opacity,
        scale: shouldAnimate ? parallaxScale : scale,
        willChange: shouldAnimate ? "transform, opacity" : "auto",
      }}
    >
      {children}
    </motion.div>
  );
};

interface CosmicNebulaProps {
  isDarkMode: boolean;
}

const CosmicNebula: React.FC<CosmicNebulaProps> = ({ isDarkMode }) => {
  const [performanceConfig, setPerformanceConfig] =
    useState<PerformanceConfig | null>(null);
  const deviceInfo = useDeviceDetection();

  useEffect(() => {
    const manager = getGlobalPerformanceManager();
    setPerformanceConfig(manager.getConfig());

    const unsubscribe = manager.onQualityAdjustment((config) => {
      setPerformanceConfig(config);
    });

    return () => {
      // Note: We don't destroy the manager here as it's global
    };
  }, []);

  // Adaptive particle count based on performance
  const dustParticleCount = Math.min(
    30,
    Math.floor((performanceConfig?.maxParticles || 200) * 0.15)
  );
  const starCount = Math.min(
    100,
    Math.floor((performanceConfig?.maxParticles || 200) * 0.5)
  );

  const shouldAnimate =
    performanceConfig?.enableAnimations && !deviceInfo.prefersReducedMotion;

  return (
    <div className="absolute inset-0 overflow-hidden">
      {/* Deep space nebula layers */}
      <motion.div
        className="absolute inset-0"
        style={{
          background: isDarkMode
            ? `
              radial-gradient(ellipse 1200px 600px at 20% 30%, rgba(147, 51, 234, 0.12), transparent),
              radial-gradient(ellipse 800px 400px at 80% 70%, rgba(59, 130, 246, 0.08), transparent),
              radial-gradient(ellipse 600px 300px at 50% 90%, rgba(251, 146, 60, 0.06), transparent),
              radial-gradient(ellipse 400px 200px at 10% 80%, rgba(236, 72, 153, 0.04), transparent)
            `
            : `
              radial-gradient(ellipse 1200px 600px at 30% 40%, rgba(99, 102, 241, 0.08), transparent),
              radial-gradient(ellipse 800px 400px at 70% 60%, rgba(168, 85, 247, 0.06), transparent),
              radial-gradient(ellipse 600px 300px at 50% 80%, rgba(251, 146, 60, 0.04), transparent)
            `,
        }}
        animate={
          shouldAnimate
            ? {
                opacity: [0.4, 0.8, 0.4],
                scale: [1, 1.05, 1],
              }
            : {}
        }
        transition={{
          duration: 20,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />

      {/* Cosmic dust particles */}
      {shouldAnimate &&
        [...Array(dustParticleCount)].map((_, i) => (
          <motion.div
            key={`cosmic-dust-${i}`}
            className="absolute bg-white/10 rounded-full"
            style={{
              width: `${Math.random() * 3 + 1}px`,
              height: `${Math.random() * 3 + 1}px`,
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              x: [0, (Math.random() - 0.5) * 200],
              y: [0, (Math.random() - 0.5) * 200],
              opacity: [0, 0.8, 0],
            }}
            transition={{
              duration: 15 + Math.random() * 10,
              repeat: Infinity,
              delay: Math.random() * 10,
              ease: "linear",
            }}
          />
        ))}

      {/* Distant stars */}
      {shouldAnimate &&
        [...Array(starCount)].map((_, i) => {
          const size = Math.random() * 2 + 0.5;
          const brightness = Math.random();
          return (
            <motion.div
              key={`distant-star-${i}`}
              className="absolute rounded-full"
              style={{
                width: `${size}px`,
                height: `${size}px`,
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                backgroundColor: `rgba(255, 255, 255, ${brightness})`,
                boxShadow:
                  performanceConfig?.shaderQuality === "high"
                    ? `0 0 ${size * 2}px rgba(255, 255, 255, ${
                        brightness * 0.5
                      })`
                    : "none",
              }}
              animate={{
                opacity: [brightness * 0.5, brightness, brightness * 0.5],
              }}
              transition={{
                duration: 3 + Math.random() * 4,
                repeat: Infinity,
                ease: "easeInOut",
                delay: Math.random() * 5,
              }}
            />
          );
        })}
    </div>
  );
};

interface PlanetaryObjectsProps {
  isDarkMode: boolean;
}

const PlanetaryObjects: React.FC<PlanetaryObjectsProps> = ({ isDarkMode }) => {
  const { scrollY } = useScroll();

  // Different planets with varying parallax speeds
  const planets = [
    {
      size: 60,
      color: "rgba(251, 146, 60, 0.3)",
      position: { x: 85, y: 20 },
      speed: -0.3,
    },
    {
      size: 40,
      color: "rgba(59, 130, 246, 0.25)",
      position: { x: 10, y: 60 },
      speed: -0.5,
    },
    {
      size: 80,
      color: "rgba(147, 51, 234, 0.2)",
      position: { x: 75, y: 80 },
      speed: -0.2,
    },
  ];

  return (
    <div className="absolute inset-0 overflow-hidden">
      {planets.map((planet, index) => {
        const y = useTransform(scrollY, [0, 1000], [0, planet.speed * 200]);
        const rotate = useTransform(
          scrollY,
          [0, 1000],
          [0, 360 * planet.speed]
        );

        return (
          <motion.div
            key={`planet-${index}`}
            className="absolute rounded-full"
            style={{
              width: `${planet.size}px`,
              height: `${planet.size}px`,
              left: `${planet.position.x}%`,
              top: `${planet.position.y}%`,
              background: `radial-gradient(circle at 30% 30%, ${planet.color}, transparent)`,
              y,
              rotate,
            }}
          />
        );
      })}
    </div>
  );
};

interface EnhancedParallaxProps {
  children: React.ReactNode;
  isDarkMode: boolean;
}

const EnhancedParallax: React.FC<EnhancedParallaxProps> = ({
  children,
  isDarkMode,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const deviceInfo = useDeviceDetection();

  return (
    <div
      ref={containerRef}
      className="relative min-h-screen w-full overflow-hidden"
    >
      {/* Background Layer - Deepest parallax */}
      <ParallaxLayer
        speed={-0.8}
        zIndex={zIndex.background}
        className="pointer-events-none"
      >
        {isDarkMode ? (
          <ParticleBackground />
        ) : (
          <div className="absolute inset-0 bg-gradient-to-br from-indigo-200 via-white to-pink-100" />
        )}
      </ParallaxLayer>

      {/* Cosmic Nebula Layer */}
      <ParallaxLayer
        speed={-0.6}
        zIndex={zIndex.nebula}
        className="pointer-events-none"
      >
        <CosmicNebula isDarkMode={isDarkMode} />
      </ParallaxLayer>

      {/* Planetary Objects Layer */}
      <ParallaxLayer
        speed={-0.4}
        zIndex={zIndex.planets}
        className="pointer-events-none"
      >
        <PlanetaryObjects isDarkMode={isDarkMode} />
      </ParallaxLayer>

      {/* Gargantua Black Hole - Midground */}
      <ParallaxLayer
        speed={-0.2}
        zIndex={zIndex.blackhole}
        className="pointer-events-none"
      >
        <GargantuaBlackHole className="w-full h-full absolute inset-0" />
      </ParallaxLayer>

      {/* Foreground Content Layer */}
      <div className="relative" style={{ zIndex: zIndex.content }}>
        {children}
      </div>
    </div>
  );
};

export default EnhancedParallax;
