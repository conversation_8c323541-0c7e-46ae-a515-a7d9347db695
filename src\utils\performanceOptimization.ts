// Performance optimization utilities for the portfolio

export interface PerformanceConfig {
  enableHighQualityShaders: boolean;
  enableParallax: boolean;
  enableAnimations: boolean;
  maxParticles: number;
  shaderQuality: 'low' | 'medium' | 'high';
  frameRateTarget: number;
}

// Detect device capabilities
export const detectDeviceCapabilities = (): PerformanceConfig => {
  const canvas = document.createElement('canvas');
  const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
  
  // Check for WebGL support
  const hasWebGL = !!gl;
  
  // Check for reduced motion preference
  const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  
  // Estimate device performance based on various factors
  const deviceMemory = (navigator as any).deviceMemory || 4; // Default to 4GB if not available
  const hardwareConcurrency = navigator.hardwareConcurrency || 4;
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  const isLowEndDevice = deviceMemory < 4 || hardwareConcurrency < 4;
  
  // Check WebGL capabilities
  let maxTextureSize = 2048;
  let maxRenderbufferSize = 2048;
  
  if (gl) {
    maxTextureSize = gl.getParameter(gl.MAX_TEXTURE_SIZE);
    maxRenderbufferSize = gl.getParameter(gl.MAX_RENDERBUFFER_SIZE);
  }
  
  // Determine performance tier
  const isHighEnd = !isMobile && deviceMemory >= 8 && hardwareConcurrency >= 8 && maxTextureSize >= 4096;
  const isMidTier = !isLowEndDevice && hasWebGL && maxTextureSize >= 2048;
  
  return {
    enableHighQualityShaders: hasWebGL && !prefersReducedMotion && (isHighEnd || isMidTier),
    enableParallax: !prefersReducedMotion && !isMobile,
    enableAnimations: !prefersReducedMotion,
    maxParticles: isHighEnd ? 1000 : isMidTier ? 500 : 200,
    shaderQuality: isHighEnd ? 'high' : isMidTier ? 'medium' : 'low',
    frameRateTarget: isMobile ? 30 : 60,
  };
};

// Frame rate monitor
export class FrameRateMonitor {
  private frameCount = 0;
  private lastTime = performance.now();
  private fps = 60;
  private callback?: (fps: number) => void;
  private animationId?: number;

  constructor(callback?: (fps: number) => void) {
    this.callback = callback;
    this.start();
  }

  private start() {
    const measure = (currentTime: number) => {
      this.frameCount++;
      
      if (currentTime - this.lastTime >= 1000) {
        this.fps = Math.round((this.frameCount * 1000) / (currentTime - this.lastTime));
        this.frameCount = 0;
        this.lastTime = currentTime;
        
        if (this.callback) {
          this.callback(this.fps);
        }
      }
      
      this.animationId = requestAnimationFrame(measure);
    };
    
    this.animationId = requestAnimationFrame(measure);
  }

  getFPS(): number {
    return this.fps;
  }

  stop() {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
    }
  }
}

// Adaptive quality manager
export class AdaptiveQualityManager {
  private config: PerformanceConfig;
  private frameRateMonitor: FrameRateMonitor;
  private qualityAdjustmentCallbacks: Array<(config: PerformanceConfig) => void> = [];

  constructor(initialConfig?: Partial<PerformanceConfig>) {
    this.config = { ...detectDeviceCapabilities(), ...initialConfig };
    
    this.frameRateMonitor = new FrameRateMonitor((fps) => {
      this.adjustQualityBasedOnFPS(fps);
    });
  }

  private adjustQualityBasedOnFPS(fps: number) {
    const targetFPS = this.config.frameRateTarget;
    
    // If FPS is consistently low, reduce quality
    if (fps < targetFPS * 0.8) {
      if (this.config.shaderQuality === 'high') {
        this.config.shaderQuality = 'medium';
        this.notifyQualityChange();
      } else if (this.config.shaderQuality === 'medium') {
        this.config.shaderQuality = 'low';
        this.config.maxParticles = Math.max(100, this.config.maxParticles * 0.7);
        this.notifyQualityChange();
      }
    }
    
    // If FPS is consistently high, we could increase quality (but be conservative)
    else if (fps > targetFPS * 1.2 && this.config.shaderQuality === 'low') {
      this.config.shaderQuality = 'medium';
      this.notifyQualityChange();
    }
  }

  private notifyQualityChange() {
    this.qualityAdjustmentCallbacks.forEach(callback => callback(this.config));
  }

  onQualityAdjustment(callback: (config: PerformanceConfig) => void) {
    this.qualityAdjustmentCallbacks.push(callback);
  }

  getConfig(): PerformanceConfig {
    return { ...this.config };
  }

  updateConfig(updates: Partial<PerformanceConfig>) {
    this.config = { ...this.config, ...updates };
    this.notifyQualityChange();
  }

  destroy() {
    this.frameRateMonitor.stop();
    this.qualityAdjustmentCallbacks = [];
  }
}

// Shader optimization utilities
export const getOptimizedShaderDefines = (quality: 'low' | 'medium' | 'high'): string => {
  const defines: string[] = [];
  
  switch (quality) {
    case 'high':
      defines.push('#define HIGH_QUALITY');
      defines.push('#define MAX_ITERATIONS 8');
      defines.push('#define ENABLE_NOISE');
      defines.push('#define ENABLE_TURBULENCE');
      break;
    case 'medium':
      defines.push('#define MEDIUM_QUALITY');
      defines.push('#define MAX_ITERATIONS 4');
      defines.push('#define ENABLE_NOISE');
      break;
    case 'low':
      defines.push('#define LOW_QUALITY');
      defines.push('#define MAX_ITERATIONS 2');
      break;
  }
  
  return defines.join('\n') + '\n';
};

// Memory management utilities
export const createTexturePool = (gl: WebGLRenderingContext, maxTextures: number = 10) => {
  const pool: WebGLTexture[] = [];
  const inUse = new Set<WebGLTexture>();
  
  return {
    acquire(): WebGLTexture | null {
      // Try to reuse an existing texture
      for (const texture of pool) {
        if (!inUse.has(texture)) {
          inUse.add(texture);
          return texture;
        }
      }
      
      // Create new texture if pool isn't full
      if (pool.length < maxTextures) {
        const texture = gl.createTexture();
        if (texture) {
          pool.push(texture);
          inUse.add(texture);
          return texture;
        }
      }
      
      return null;
    },
    
    release(texture: WebGLTexture) {
      inUse.delete(texture);
    },
    
    cleanup() {
      pool.forEach(texture => gl.deleteTexture(texture));
      pool.length = 0;
      inUse.clear();
    }
  };
};

// Intersection Observer for performance
export const createPerformantIntersectionObserver = (
  callback: IntersectionObserverCallback,
  options?: IntersectionObserverInit
) => {
  const defaultOptions: IntersectionObserverInit = {
    rootMargin: '50px',
    threshold: [0, 0.25, 0.5, 0.75, 1],
    ...options,
  };
  
  return new IntersectionObserver(callback, defaultOptions);
};

// Debounced resize handler
export const createDebouncedResizeHandler = (
  callback: () => void,
  delay: number = 250
) => {
  let timeoutId: NodeJS.Timeout;
  
  const handler = () => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(callback, delay);
  };
  
  return {
    handler,
    cleanup: () => clearTimeout(timeoutId),
  };
};

// Global performance manager instance
let globalPerformanceManager: AdaptiveQualityManager | null = null;

export const getGlobalPerformanceManager = (): AdaptiveQualityManager => {
  if (!globalPerformanceManager) {
    globalPerformanceManager = new AdaptiveQualityManager();
  }
  return globalPerformanceManager;
};

export const destroyGlobalPerformanceManager = () => {
  if (globalPerformanceManager) {
    globalPerformanceManager.destroy();
    globalPerformanceManager = null;
  }
};
