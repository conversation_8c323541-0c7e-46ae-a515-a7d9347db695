// Gargantua Black Hole Vertex Shader
// Based on <PERSON><PERSON>'s gravitational lensing models from Interstellar

varying vec2 vUv;
varying vec3 vPosition;
varying vec3 vNormal;
varying vec3 vWorldPosition;

uniform float time;
uniform float blackHoleRadius;
uniform vec3 blackHolePosition;

void main() {
    vUv = uv;
    vNormal = normalize(normalMatrix * normal);
    
    // Transform position to world space
    vec4 worldPosition = modelMatrix * vec4(position, 1.0);
    vWorldPosition = worldPosition.xyz;
    vPosition = position;
    
    // Apply gravitational time dilation effect near the black hole
    float distanceToBlackHole = length(worldPosition.xyz - blackHolePosition);
    float gravitationalFactor = 1.0 - (blackHoleRadius / distanceToBlackHole);
    gravitationalFactor = clamp(gravitationalFactor, 0.1, 1.0);
    
    // Slight warping effect for vertices near the black hole
    vec3 direction = normalize(worldPosition.xyz - blackHolePosition);
    float warpStrength = blackHoleRadius / (distanceToBlackHole * distanceToBlackHole);
    warpStrength = clamp(warpStrength * 0.1, 0.0, 0.05);
    
    worldPosition.xyz += direction * warpStrength * sin(time * 2.0);
    
    gl_Position = projectionMatrix * viewMatrix * worldPosition;
}
