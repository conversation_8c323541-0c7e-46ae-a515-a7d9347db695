// Gargantua Black Hole Vertex Shader
// Enhanced for scientifically accurate gravitational effects

varying vec2 vUv;
varying vec3 vPosition;
varying vec3 vNormal;
varying vec3 vWorldPosition;

uniform float time;
uniform float blackHoleRadius;
uniform vec3 blackHolePosition;
uniform float intensity;

void main() {
    vUv = uv;
    vNormal = normalize(normalMatrix * normal);
    vPosition = position;

    // Transform position to world space
    vec4 worldPosition = modelMatrix * vec4(position, 1.0);
    vWorldPosition = worldPosition.xyz;

    // Apply subtle frame-dragging effect (Lense-Thirring precession)
    float distanceToBlackHole = length(worldPosition.xyz - blackHolePosition);
    float frameDragging = blackHoleRadius / (distanceToBlackHole * distanceToBlackHole);
    frameDragging = clamp(frameDragging * 0.05, 0.0, 0.02);

    // Rotate position slightly due to frame dragging
    float angle = frameDragging * time * 0.1;
    mat2 rotation = mat2(cos(angle), -sin(angle), sin(angle), cos(angle));
    worldPosition.xz = blackHolePosition.xz + rotation * (worldPosition.xz - blackHolePosition.xz);

    // Apply gravitational time dilation for subtle vertex animation
    float timeDilation = 1.0 - (blackHoleRadius / distanceToBlackHole);
    timeDilation = clamp(timeDilation, 0.5, 1.0);

    // Subtle spacetime curvature effect
    vec3 direction = normalize(worldPosition.xyz - blackHolePosition);
    float curvature = blackHoleRadius / (distanceToBlackHole * distanceToBlackHole);
    curvature = clamp(curvature * 0.02, 0.0, 0.01);

    worldPosition.xyz += direction * curvature * sin(time * timeDilation);

    gl_Position = projectionMatrix * viewMatrix * worldPosition;
}
