// Layout system utilities for consistent spacing and responsive design

export interface SpacingScale {
  xs: string;
  sm: string;
  md: string;
  lg: string;
  xl: string;
  '2xl': string;
  '3xl': string;
  '4xl': string;
}

export interface BreakpointConfig {
  sm: number;
  md: number;
  lg: number;
  xl: number;
  '2xl': number;
}

// Consistent spacing scale based on 8px grid
export const spacing: SpacingScale = {
  xs: '0.5rem',    // 8px
  sm: '1rem',      // 16px
  md: '1.5rem',    // 24px
  lg: '2rem',      // 32px
  xl: '3rem',      // 48px
  '2xl': '4rem',   // 64px
  '3xl': '6rem',   // 96px
  '4xl': '8rem',   // 128px
};

// Tailwind-compatible breakpoints
export const breakpoints: BreakpointConfig = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
};

// Z-index layers for consistent stacking
export const zIndex = {
  background: 0,
  nebula: 5,
  planets: 8,
  blackhole: 10,
  particles: 15,
  content: 30,
  navigation: 40,
  modal: 50,
  tooltip: 60,
  dropdown: 70,
  overlay: 80,
  notification: 90,
  debug: 100,
} as const;

// Container classes for consistent max-widths
export const containerClasses = {
  xs: 'max-w-xs mx-auto px-4',
  sm: 'max-w-sm mx-auto px-4',
  md: 'max-w-md mx-auto px-4',
  lg: 'max-w-lg mx-auto px-6',
  xl: 'max-w-xl mx-auto px-6',
  '2xl': 'max-w-2xl mx-auto px-6',
  '3xl': 'max-w-3xl mx-auto px-8',
  '4xl': 'max-w-4xl mx-auto px-8',
  '5xl': 'max-w-5xl mx-auto px-8',
  '6xl': 'max-w-6xl mx-auto px-8',
  '7xl': 'max-w-7xl mx-auto px-8',
  full: 'max-w-full mx-auto px-4 sm:px-6 lg:px-8',
} as const;

// Grid system utilities
export const gridClasses = {
  // Basic grid layouts
  cols1: 'grid grid-cols-1',
  cols2: 'grid grid-cols-1 md:grid-cols-2',
  cols3: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
  cols4: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
  cols6: 'grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6',
  
  // Responsive grid gaps
  gapXs: 'gap-2',
  gapSm: 'gap-4',
  gapMd: 'gap-6',
  gapLg: 'gap-8',
  gapXl: 'gap-12',
  gap2xl: 'gap-16',
  
  // Auto-fit grids
  autoFit: 'grid grid-cols-[repeat(auto-fit,minmax(280px,1fr))]',
  autoFill: 'grid grid-cols-[repeat(auto-fill,minmax(280px,1fr))]',
} as const;

// Flexbox utilities
export const flexClasses = {
  // Basic flex layouts
  row: 'flex flex-row',
  col: 'flex flex-col',
  rowReverse: 'flex flex-row-reverse',
  colReverse: 'flex flex-col-reverse',
  
  // Responsive flex direction
  responsiveRow: 'flex flex-col md:flex-row',
  responsiveCol: 'flex flex-row md:flex-col',
  
  // Alignment
  center: 'justify-center items-center',
  centerX: 'justify-center',
  centerY: 'items-center',
  spaceBetween: 'justify-between',
  spaceAround: 'justify-around',
  spaceEvenly: 'justify-evenly',
  
  // Flex wrap
  wrap: 'flex-wrap',
  nowrap: 'flex-nowrap',
  wrapReverse: 'flex-wrap-reverse',
} as const;

// Section spacing utilities
export const sectionClasses = {
  // Vertical spacing between sections
  spacingXs: 'space-y-8',
  spacingSm: 'space-y-12',
  spacingMd: 'space-y-16 md:space-y-20',
  spacingLg: 'space-y-20 md:space-y-24 lg:space-y-32',
  spacingXl: 'space-y-24 md:space-y-32 lg:space-y-40',
  
  // Section padding
  paddingXs: 'py-8',
  paddingSm: 'py-12',
  paddingMd: 'py-16 md:py-20',
  paddingLg: 'py-20 md:py-24 lg:py-32',
  paddingXl: 'py-24 md:py-32 lg:py-40',
} as const;

// Responsive text utilities
export const textClasses = {
  // Headings
  h1: 'text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold',
  h2: 'text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-bold',
  h3: 'text-xl md:text-2xl lg:text-3xl font-semibold',
  h4: 'text-lg md:text-xl lg:text-2xl font-semibold',
  h5: 'text-base md:text-lg lg:text-xl font-medium',
  h6: 'text-sm md:text-base lg:text-lg font-medium',
  
  // Body text
  bodyLg: 'text-lg md:text-xl leading-relaxed',
  body: 'text-base md:text-lg leading-relaxed',
  bodySm: 'text-sm md:text-base leading-relaxed',
  
  // Captions and labels
  caption: 'text-xs md:text-sm text-gray-600 dark:text-gray-400',
  label: 'text-sm font-medium',
} as const;

// Card and component utilities
export const cardClasses = {
  // Basic card styles
  base: 'bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl',
  elevated: 'bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl shadow-lg',
  interactive: 'bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl hover:border-orange-400/50 transition-all duration-300',
  
  // Card padding
  paddingXs: 'p-4',
  paddingSm: 'p-6',
  paddingMd: 'p-6 md:p-8',
  paddingLg: 'p-8 md:p-10',
  paddingXl: 'p-10 md:p-12',
} as const;

// Animation utilities
export const animationClasses = {
  // Entrance animations
  fadeIn: 'animate-in fade-in duration-500',
  slideInUp: 'animate-in slide-in-from-bottom-4 duration-500',
  slideInDown: 'animate-in slide-in-from-top-4 duration-500',
  slideInLeft: 'animate-in slide-in-from-left-4 duration-500',
  slideInRight: 'animate-in slide-in-from-right-4 duration-500',
  
  // Hover animations
  hoverScale: 'hover:scale-105 transition-transform duration-300',
  hoverLift: 'hover:-translate-y-2 transition-transform duration-300',
  hoverGlow: 'hover:shadow-lg hover:shadow-orange-400/25 transition-shadow duration-300',
} as const;

// Utility functions
export const getCurrentBreakpoint = (): keyof BreakpointConfig | 'xs' => {
  if (typeof window === 'undefined') return 'lg';
  
  const width = window.innerWidth;
  
  if (width >= breakpoints['2xl']) return '2xl';
  if (width >= breakpoints.xl) return 'xl';
  if (width >= breakpoints.lg) return 'lg';
  if (width >= breakpoints.md) return 'md';
  if (width >= breakpoints.sm) return 'sm';
  return 'xs';
};

export const isBreakpointUp = (breakpoint: keyof BreakpointConfig): boolean => {
  if (typeof window === 'undefined') return false;
  return window.innerWidth >= breakpoints[breakpoint];
};

export const isBreakpointDown = (breakpoint: keyof BreakpointConfig): boolean => {
  if (typeof window === 'undefined') return false;
  return window.innerWidth < breakpoints[breakpoint];
};

// Class name builder utility
export const cn = (...classes: (string | undefined | null | false)[]): string => {
  return classes.filter(Boolean).join(' ');
};

// Responsive class builder
export const responsive = (
  base: string,
  sm?: string,
  md?: string,
  lg?: string,
  xl?: string,
  xl2?: string
): string => {
  const classes = [base];
  
  if (sm) classes.push(`sm:${sm}`);
  if (md) classes.push(`md:${md}`);
  if (lg) classes.push(`lg:${lg}`);
  if (xl) classes.push(`xl:${xl}`);
  if (xl2) classes.push(`2xl:${xl2}`);
  
  return classes.join(' ');
};

// Generate consistent spacing classes
export const getSpacing = (size: keyof SpacingScale): string => {
  const sizeMap = {
    xs: '2',
    sm: '4',
    md: '6',
    lg: '8',
    xl: '12',
    '2xl': '16',
    '3xl': '24',
    '4xl': '32',
  };
  
  return sizeMap[size];
};

// Generate grid template classes
export const getGridTemplate = (columns: number, minWidth: string = '280px'): string => {
  return `grid-cols-[repeat(auto-fit,minmax(${minWidth},1fr))]`;
};

export default {
  spacing,
  breakpoints,
  zIndex,
  containerClasses,
  gridClasses,
  flexClasses,
  sectionClasses,
  textClasses,
  cardClasses,
  animationClasses,
  getCurrentBreakpoint,
  isBreakpointUp,
  isBreakpointDown,
  cn,
  responsive,
  getSpacing,
  getGridTemplate,
};
